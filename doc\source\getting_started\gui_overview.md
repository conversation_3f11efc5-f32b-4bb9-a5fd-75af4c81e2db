# GUI Overview

The only difference compared with the original Abaqus GUI is the added toolbar group.

![](/_static/images/toolbar-annotated.png)

The toolbar group composes of gadget buttons (1, 2), SG creation tool buttons (3, 4, 5, 6, 7, 8), SwiftComp analysis and visualization tool buttons (9, 10, 11, 12).
Their functions can be simply described as follows:

1. Work plane
    : Set sketch plane for 1D/2D user-defined SGs.
2. New layups
    : Add new layups for 1D/2D SGs.
3. 1D SG
    : Create 1D SG, including common SGs and user-defined SGs.
4. 2D built-in SG
    : Create 2D built-in SGs.
5. Assign layups
    : Create laminate for 2D cross-sections.
6. Erase layups
    : Delete laminate for 2D cross-sections.
7. Read file
    : Create 2D cross-section from input data file.
8. 3D built-in SG
    : Create 3D built-in SGs.
9. Homogenization
    : Carry out homogenization analysis.
10. Macro model
    : Import the homogenized properties.
11. Dehomogenization
    : Carry out dehomogenization analysis.
12. Visualization
    : Visualize the results with only SwiftComp analysis files.


